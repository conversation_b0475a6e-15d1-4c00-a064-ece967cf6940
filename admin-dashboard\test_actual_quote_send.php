<?php
// Test the exact same process as clicking "Send Quote" in admin dashboard
require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';
require_once 'classes/EmailService.php';

echo "<h2>Test Actual Quote Send Process</h2>";

try {
    $quoteModel = new Quote();
    $emailService = new EmailService();

    // Get a quote
    $quotes = $quoteModel->findAll(1, 0);
    if (empty($quotes)) {
        echo "<p style='color: red;'>No quotes found</p>";
        exit;
    }

    $quote = $quotes[0];
    echo "<p><strong>Testing Quote Send Process</strong></p>";
    echo "<p><strong>Quote:</strong> " . $quote['quote_reference'] . "</p>";
    echo "<p><strong>Customer:</strong> " . $quote['customer_name'] . " (" . $quote['customer_email'] . ")</p>";

    // Simulate the exact data preparation from quotes.php
    $amount = 1500.00; // Test amount
    $quoteDetails = "This is a test quote to verify the actual sending process works correctly.";
    $includePaymentLink = true;

    // This is EXACTLY the same data structure as quotes.php lines 92-104
    $quoteEmailData = [
        'quote_id' => $quote['quote_id'],
        'quote_reference' => $quote['quote_reference'],
        'customer_name' => $quote['customer_name'],
        'customer_email' => $quote['customer_email'],
        'quoted_amount' => $amount,
        'quote_details' => $quoteDetails,
        'travel_date' => $quote['travel_date'],
        'number_of_adults' => $quote['number_of_adults'],
        'number_of_children' => $quote['number_of_children'],
        'special_requirements' => $quote['special_requirements'],
        'include_payment_link' => $includePaymentLink
    ];

    echo "<h3>Quote Email Data (same as admin dashboard):</h3>";
    echo "<ul>";
    echo "<li><strong>Quote ID:</strong> " . $quoteEmailData['quote_id'] . "</li>";
    echo "<li><strong>Reference:</strong> " . $quoteEmailData['quote_reference'] . "</li>";
    echo "<li><strong>Customer:</strong> " . $quoteEmailData['customer_name'] . "</li>";
    echo "<li><strong>Email:</strong> " . $quoteEmailData['customer_email'] . "</li>";
    echo "<li><strong>Amount:</strong> $" . $quoteEmailData['quoted_amount'] . "</li>";
    echo "<li><strong>Include Payment Link:</strong> " . ($quoteEmailData['include_payment_link'] ? 'Yes' : 'No') . "</li>";
    echo "</ul>";

    // Simulate current user (admin)
    $currentUser = [
        'user_id' => 1,
        'username' => 'admin',
        'full_name' => 'Test Admin'
    ];

    echo "<h3>Sending Quote Email (exact same call as quotes.php line 107)...</h3>";
    
    // This is EXACTLY the same call as quotes.php line 107
    $emailSent = $emailService->sendQuoteToCustomer($quoteEmailData, $currentUser);

    if ($emailSent) {
        $linkText = $includePaymentLink ? ' with payment link' : ' (payment link not included)';
        echo "<p style='color: green; font-size: 18px;'>✅ <strong>SUCCESS!</strong> Quote sent successfully to customer via email" . $linkText . ".</p>";
        
        echo "<div style='background: #f0f9ff; border: 1px solid #0ea5e9; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4 style='color: #0369a1; margin-top: 0;'>Email Details:</h4>";
        echo "<ul>";
        echo "<li><strong>From:</strong> <EMAIL></li>";
        echo "<li><strong>Reply-To:</strong> <EMAIL></li>";
        echo "<li><strong>To:</strong> " . $quote['customer_email'] . "</li>";
        echo "<li><strong>Subject:</strong> Your Travel Quote is Ready - " . $quote['quote_reference'] . " | Meleva Tours</li>";
        echo "<li><strong>Payment Link:</strong> " . ($includePaymentLink ? 'Included' : 'Not included') . "</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p><strong>✅ The actual quote sending process is now working!</strong></p>";
        echo "<p>Check the customer's email inbox (and spam folder if needed).</p>";
        
    } else {
        echo "<p style='color: red; font-size: 18px;'>❌ Quote updated but failed to send email. Please contact customer manually.</p>";
        echo "<p>This is the same error message you would see in the admin dashboard.</p>";
    }

    // Show recent error logs
    echo "<h3>Recent Error Log (last 10 relevant entries):</h3>";
    $errorLog = ini_get('error_log');
    if ($errorLog && file_exists($errorLog)) {
        $lines = file($errorLog);
        $recentLines = array_slice($lines, -10);
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        foreach ($recentLines as $line) {
            if (strpos($line, 'Quote email') !== false || strpos($line, 'SMTP') !== false) {
                echo htmlspecialchars($line);
            }
        }
        echo "</pre>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Exception: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<p><a href='quotes.php'>← Back to Quotes</a></p>";
?>
