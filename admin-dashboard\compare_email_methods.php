<?php
// Compare working sendUserConfirmation vs sendQuoteToCustomer
require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';
require_once 'classes/EmailService.php';

echo "<h2>Email Methods Comparison Test</h2>";

try {
    $quoteModel = new Quote();
    $emailService = new EmailService();

    // Get a quote for testing
    $quotes = $quoteModel->findAll(1, 0);
    if (empty($quotes)) {
        echo "<p style='color: red;'>No quotes found</p>";
        exit;
    }

    $quote = $quotes[0];
    echo "<p><strong>Testing with:</strong> " . $quote['customer_name'] . " (" . $quote['customer_email'] . ")</p>";

    // Test 1: Working sendUserConfirmation method
    echo "<h3>Test 1: Working sendUserConfirmation Method</h3>";
    
    $userData = [
        'name' => $quote['customer_name'],
        'email' => $quote['customer_email'],
        'quote_reference' => $quote['quote_reference'],
        'travel_date' => $quote['travel_date'],
        'number_of_adults' => $quote['number_of_adults'],
        'number_of_children' => $quote['number_of_children'],
        'special_requirements' => $quote['special_requirements']
    ];

    echo "<p>Calling sendUserConfirmation with 'quote' type...</p>";
    $result1 = $emailService->sendUserConfirmation($userData, 'quote');
    
    if ($result1) {
        echo "<p style='color: green;'>✅ sendUserConfirmation returned TRUE</p>";
    } else {
        echo "<p style='color: red;'>❌ sendUserConfirmation returned FALSE</p>";
    }

    // Wait a moment between tests
    sleep(2);

    // Test 2: Problem sendQuoteToCustomer method
    echo "<h3>Test 2: Problem sendQuoteToCustomer Method</h3>";
    
    $quoteData = [
        'quote_id' => $quote['quote_id'],
        'quote_reference' => $quote['quote_reference'],
        'customer_name' => $quote['customer_name'],
        'customer_email' => $quote['customer_email'],
        'quoted_amount' => 1500.00,
        'quote_details' => 'Test quote details for comparison',
        'travel_date' => $quote['travel_date'],
        'number_of_adults' => $quote['number_of_adults'],
        'number_of_children' => $quote['number_of_children'],
        'special_requirements' => $quote['special_requirements'],
        'include_payment_link' => true
    ];

    echo "<p>Calling sendQuoteToCustomer...</p>";
    $result2 = $emailService->sendQuoteToCustomer($quoteData);
    
    if ($result2) {
        echo "<p style='color: green;'>✅ sendQuoteToCustomer returned TRUE</p>";
    } else {
        echo "<p style='color: red;'>❌ sendQuoteToCustomer returned FALSE</p>";
    }

    // Summary
    echo "<h3>Results Summary:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Method</th><th>Result</th><th>Expected Behavior</th></tr>";
    echo "<tr><td>sendUserConfirmation</td><td>" . ($result1 ? "✅ SUCCESS" : "❌ FAILED") . "</td><td>Should work (emails are received)</td></tr>";
    echo "<tr><td>sendQuoteToCustomer</td><td>" . ($result2 ? "✅ SUCCESS" : "❌ FAILED") . "</td><td>Should work but emails not received</td></tr>";
    echo "</table>";

    if ($result1 && $result2) {
        echo "<div style='background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4 style='color: #92400e; margin-top: 0;'>Both methods return TRUE</h4>";
        echo "<p>If both methods return TRUE but only sendUserConfirmation emails are received, the issue might be:</p>";
        echo "<ul>";
        echo "<li>Email template generation in sendQuoteToCustomer</li>";
        echo "<li>Email headers or content causing spam filtering</li>";
        echo "<li>Different email routing or processing</li>";
        echo "</ul>";
        echo "</div>";
    } else if ($result1 && !$result2) {
        echo "<div style='background: #fee2e2; border: 1px solid #ef4444; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4 style='color: #dc2626; margin-top: 0;'>sendQuoteToCustomer is failing</h4>";
        echo "<p>The sendQuoteToCustomer method is returning FALSE, indicating an error in the method itself.</p>";
        echo "</div>";
    }

    // Check error logs
    echo "<h3>Recent Error Log:</h3>";
    $errorLog = ini_get('error_log');
    if ($errorLog && file_exists($errorLog)) {
        $lines = file($errorLog);
        $recentLines = array_slice($lines, -15);
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        foreach ($recentLines as $line) {
            if (strpos($line, 'Quote email') !== false || 
                strpos($line, 'User confirmation') !== false || 
                strpos($line, 'SMTP') !== false ||
                strpos($line, 'PHPMailer') !== false) {
                echo htmlspecialchars($line);
            }
        }
        echo "</pre>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Exception: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<p><a href='quotes.php'>← Back to Quotes</a></p>";
?>
