<?php
// Test script to debug quote email delivery
require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';
require_once 'classes/EmailService.php';

// Initialize models
$quoteModel = new Quote();
$emailService = new EmailService();

echo "<h2>Quote Email Delivery Test</h2>";

// Get the most recent quote for testing
$recentQuotes = $quoteModel->getAll(1, 1); // Get 1 quote, page 1
if (empty($recentQuotes)) {
    echo "<p style='color: red;'>No quotes found in database. Please create a quote first.</p>";
    exit;
}

$quote = $recentQuotes[0];
echo "<h3>Testing with Quote:</h3>";
echo "<pre>";
print_r($quote);
echo "</pre>";

// Test email configuration
echo "<h3>Email Configuration Test:</h3>";
$reflection = new ReflectionClass($emailService);
$emailConfigProperty = $reflection->getProperty('emailConfig');
$emailConfigProperty->setAccessible(true);
$emailConfig = $emailConfigProperty->getValue($emailService);

echo "<pre>";
print_r($emailConfig);
echo "</pre>";

// Test SMTP connection
echo "<h3>SMTP Connection Test:</h3>";
try {
    $setupMethod = $reflection->getMethod('setupSMTP');
    $setupMethod->setAccessible(true);
    $setupMethod->invoke($emailService);
    echo "<p style='color: green;'>✅ SMTP setup successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ SMTP setup failed: " . $e->getMessage() . "</p>";
}

// Prepare test quote data
$testQuoteData = [
    'quote_id' => $quote['quote_id'],
    'quote_reference' => $quote['quote_reference'],
    'customer_name' => $quote['customer_name'],
    'customer_email' => $quote['customer_email'],
    'quoted_amount' => 1500.00,
    'quote_details' => 'Test quote details for debugging',
    'travel_date' => $quote['travel_date'],
    'number_of_adults' => $quote['number_of_adults'],
    'number_of_children' => $quote['number_of_children'],
    'special_requirements' => $quote['special_requirements'],
    'include_payment_link' => true
];

echo "<h3>Test Quote Email Data:</h3>";
echo "<pre>";
print_r($testQuoteData);
echo "</pre>";

// Test sending quote email
echo "<h3>Sending Test Quote Email:</h3>";
try {
    $result = $emailService->sendQuoteToCustomer($testQuoteData);
    if ($result) {
        echo "<p style='color: green;'>✅ Quote email sent successfully!</p>";
    } else {
        echo "<p style='color: red;'>❌ Quote email failed to send</p>";
        
        // Get PHPMailer error info
        $mailProperty = $reflection->getProperty('mail');
        $mailProperty->setAccessible(true);
        $mail = $mailProperty->getValue($emailService);
        echo "<p><strong>PHPMailer Error:</strong> " . $mail->ErrorInfo . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception occurred: " . $e->getMessage() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Check PHP error log
echo "<h3>Recent PHP Error Log:</h3>";
$errorLogPath = ini_get('error_log');
if ($errorLogPath && file_exists($errorLogPath)) {
    $logLines = file($errorLogPath);
    $recentLines = array_slice($logLines, -20); // Last 20 lines
    echo "<pre>";
    foreach ($recentLines as $line) {
        if (strpos($line, 'email') !== false || strpos($line, 'SMTP') !== false || strpos($line, 'mail') !== false) {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "<p>Error log path not found or not accessible: " . $errorLogPath . "</p>";
}

echo "<h3>Troubleshooting Checklist:</h3>";
echo "<ul>";
echo "<li>✅ Check SMTP credentials (host, port, username, password)</li>";
echo "<li>✅ Verify email addresses are valid</li>";
echo "<li>✅ Check if emails are going to spam folder</li>";
echo "<li>✅ Verify cPanel email account exists and is active</li>";
echo "<li>✅ Check server firewall allows SMTP connections on port 465</li>";
echo "<li>✅ Verify SSL certificate is valid for the domain</li>";
echo "</ul>";

echo "<p><a href='quotes.php'>← Back to Quotes</a></p>";
?>
