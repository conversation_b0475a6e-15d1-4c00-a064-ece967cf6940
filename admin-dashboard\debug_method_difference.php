<?php
// Debug the exact difference between working and non-working methods
require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';
require_once 'classes/EmailService.php';

echo "<h2>Method Execution Debug</h2>";

try {
    $quoteModel = new Quote();
    $emailService = new EmailService();

    // Get a quote
    $quotes = $quoteModel->findAll(1, 0);
    if (empty($quotes)) {
        echo "<p style='color: red;'>No quotes found</p>";
        exit;
    }

    $quote = $quotes[0];
    echo "<p><strong>Testing with:</strong> " . $quote['customer_name'] . " (" . $quote['customer_email'] . ")</p>";

    // Use reflection to access private methods and properties
    $reflection = new ReflectionClass($emailService);
    $mailProperty = $reflection->getProperty('mail');
    $mailProperty->setAccessible(true);
    $mail = $mailProperty->getValue($emailService);

    echo "<h3>Step-by-Step Method Execution Debug</h3>";

    // Test 1: Manual execution of sendQuoteToCustomer logic
    echo "<h4>Test 1: Manual sendQuoteToCustomer Logic</h4>";
    
    $quoteData = [
        'quote_id' => $quote['quote_id'],
        'quote_reference' => $quote['quote_reference'],
        'customer_name' => $quote['customer_name'],
        'customer_email' => $quote['customer_email'],
        'quoted_amount' => 1500.00,
        'quote_details' => 'Manual test quote',
        'travel_date' => $quote['travel_date'],
        'number_of_adults' => $quote['number_of_adults'],
        'number_of_children' => $quote['number_of_children'],
        'special_requirements' => $quote['special_requirements'],
        'include_payment_link' => true
    ];

    try {
        // Step 1: Clear addresses (same as method)
        $mail->clearAddresses();
        $mail->clearReplyTos();
        echo "<p>✅ Step 1: Cleared addresses and reply-tos</p>";

        // Step 2: Set from address (same as method)
        $emailConfigProperty = $reflection->getProperty('emailConfig');
        $emailConfigProperty->setAccessible(true);
        $emailConfig = $emailConfigProperty->getValue($emailService);
        
        $mail->setFrom(
            $emailConfig['user_confirmations']['from_email'], // <EMAIL>
            'Meleva Tours and Travel - Booking Department'
        );
        echo "<p>✅ Step 2: Set from address to " . $emailConfig['user_confirmations']['from_email'] . "</p>";

        // Step 3: Set reply-to (same as method)
        $contactModel = new ContactInfo();
        $contactInfo = $contactModel->getCurrent();
        $bookingEmail = $contactInfo['booking_email'] ?? '<EMAIL>';
        $mail->addReplyTo($bookingEmail, 'Meleva Tours & Travel - Booking Department');
        echo "<p>✅ Step 3: Set reply-to to " . $bookingEmail . "</p>";

        // Step 4: Add recipient (same as method)
        $mail->addAddress($quoteData['customer_email'], $quoteData['customer_name']);
        echo "<p>✅ Step 4: Added recipient " . $quoteData['customer_email'] . "</p>";

        // Step 5: Set email content (same as method)
        $mail->isHTML(true);
        $mail->Subject = 'Your Travel Quote is Ready - ' . $quoteData['quote_reference'] . ' | Meleva Tours';
        echo "<p>✅ Step 5: Set subject</p>";

        // Step 6: Add headers (same as method)
        $conversationId = 'QUOTE_' . $quoteData['quote_reference'];
        $messageIdMethod = $reflection->getMethod('generateEmailMessageId');
        $messageIdMethod->setAccessible(true);
        $messageId = $messageIdMethod->invoke($emailService);
        
        $mail->addCustomHeader('Message-ID', $messageId);
        $mail->addCustomHeader('X-Conversation-ID', $conversationId);
        $mail->addCustomHeader('X-Quote-Reference', $quoteData['quote_reference']);
        echo "<p>✅ Step 6: Added custom headers</p>";

        // Step 7: Generate email templates (this might be where it fails)
        echo "<p><strong>Step 7: Generating email templates...</strong></p>";
        
        $htmlMethod = $reflection->getMethod('getQuoteEmailTemplate');
        $htmlMethod->setAccessible(true);
        $htmlTemplate = $htmlMethod->invoke($emailService, $quoteData, null);
        
        if (empty($htmlTemplate)) {
            echo "<p style='color: red;'>❌ HTML template is EMPTY!</p>";
            throw new Exception("HTML template generation failed");
        } else {
            echo "<p>✅ HTML template generated (" . strlen($htmlTemplate) . " characters)</p>";
        }

        $plainMethod = $reflection->getMethod('getQuoteEmailPlainText');
        $plainMethod->setAccessible(true);
        $plainTemplate = $plainMethod->invoke($emailService, $quoteData, null);
        
        if (empty($plainTemplate)) {
            echo "<p style='color: red;'>❌ Plain text template is EMPTY!</p>";
            throw new Exception("Plain text template generation failed");
        } else {
            echo "<p>✅ Plain text template generated (" . strlen($plainTemplate) . " characters)</p>";
        }

        $mail->Body = $htmlTemplate;
        $mail->AltBody = $plainTemplate;
        echo "<p>✅ Step 7: Templates set successfully</p>";

        // Step 8: Send email
        echo "<p><strong>Step 8: Sending email...</strong></p>";
        $result = $mail->send();
        
        if ($result) {
            echo "<p style='color: green; font-size: 18px;'>✅ <strong>MANUAL METHOD SUCCEEDED!</strong></p>";
            echo "<p>Email should be delivered to: " . $quoteData['customer_email'] . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Manual method failed</p>";
            echo "<p><strong>PHPMailer Error:</strong> " . $mail->ErrorInfo . "</p>";
        }

    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Manual method exception: " . $e->getMessage() . "</p>";
        echo "<p><strong>This tells us exactly where the sendQuoteToCustomer method is failing!</strong></p>";
    }

    // Test 2: Call the actual method for comparison
    echo "<h4>Test 2: Actual sendQuoteToCustomer Method Call</h4>";
    
    try {
        $actualResult = $emailService->sendQuoteToCustomer($quoteData, null);
        
        if ($actualResult) {
            echo "<p style='color: green;'>✅ Actual method returned TRUE</p>";
        } else {
            echo "<p style='color: red;'>❌ Actual method returned FALSE</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Actual method exception: " . $e->getMessage() . "</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Setup error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<p><a href='quotes.php'>← Back to Quotes</a></p>";
?>
